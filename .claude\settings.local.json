{"permissions": {"allow": ["Bash(claude --version)", "Bash(npm update:*)", "Bash(npm uninstall:*)", "Bash(npm install:*)", "Bash(where git)", "<PERSON><PERSON>(setx:*)", "Bash(dir \"C:\\Program Files\\Git\\bin\\bash.exe\")", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "Bash(npx create-next-app:*)", "<PERSON><PERSON>(npx husky:*)", "Bash(git init:*)", "Bash(rm:*)", "Bash(npm run lint)", "Bash(npm run build:*)", "Bash(npm test)", "Bash(npm run test:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(timeout 30 npm run dev)", "Bash(ls:*)", "Bash(grep:*)", "Bash(npm run:*)", "Bash(node:*)", "Bash(npm test:*)", "<PERSON><PERSON>(cat:*)", "Bash(timeout 5 curl -s http://localhost:3000/api/health)"], "deny": []}}
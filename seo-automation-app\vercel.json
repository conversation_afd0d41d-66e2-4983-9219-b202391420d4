{"version": 2, "builds": [{"src": "next.config.ts", "use": "@vercel/next"}], "functions": {"src/app/api/content/generate/route.ts": {"maxDuration": 300, "memory": 3008}, "src/app/api/serp/analyze/route.ts": {"maxDuration": 60, "memory": 1024}, "src/app/api/content/scrape/route.ts": {"maxDuration": 120, "memory": 1024}, "src/app/api/ai/generate/route.ts": {"maxDuration": 180, "memory": 1024}, "src/app/api/health/route.ts": {"maxDuration": 10}, "src/app/api/metrics/route.ts": {"maxDuration": 10}}, "regions": ["iad1"], "buildCommand": "npm run build", "installCommand": "npm ci", "framework": "nextjs", "crons": [{"path": "/api/health", "schedule": "0 */5 * * *"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://yourdomain.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase-url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role-key", "OPENAI_API_KEY": "@openai-api-key", "ANTHROPIC_API_KEY": "@anthropic-api-key", "SERPER_API_KEY": "@serper-api-key", "FIRECRAWL_API_KEY": "@firecrawl-api-key", "NEXTAUTH_SECRET": "@nextauth-secret", "NEXT_PUBLIC_SITE_URL": "@site-url"}}
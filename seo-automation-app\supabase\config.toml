# Supabase configuration file for SEO Automation App

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
enable_signup = true
email_confirm = false

[db]
port = 54322
major_version = 15

[studio]
enabled = true
port = 54323

[functions]
enabled = true
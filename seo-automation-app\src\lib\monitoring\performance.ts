import { trackPerformance, trackError } from '@/lib/analytics/vercel';\nimport { getEnv } from '@/lib/env/validation';\n\n// Performance thresholds (in milliseconds)\nconst PERFORMANCE_BUDGETS = {\n  // Core Web Vitals\n  FCP: 1500,  // First Contentful Paint\n  LCP: 2500,  // Largest Contentful Paint\n  FID: 100,   // First Input Delay\n  CLS: 0.1,   // Cumulative Layout Shift\n  TTFB: 800,  // Time to First Byte\n  \n  // Custom metrics\n  API_RESPONSE: 5000,      // API response time\n  DATABASE_QUERY: 1000,    // Database query time\n  CONTENT_GENERATION: 30000, // Content generation time\n  SERP_ANALYSIS: 10000,    // SERP analysis time\n};\n\n// Performance monitoring class\nclass PerformanceMonitor {\n  private static instance: PerformanceMonitor;\n  private metrics: Map<string, number[]> = new Map();\n  private alerts: Map<string, number> = new Map();\n  \n  private constructor() {\n    this.setupPerformanceObserver();\n  }\n  \n  static getInstance(): PerformanceMonitor {\n    if (!PerformanceMonitor.instance) {\n      PerformanceMonitor.instance = new PerformanceMonitor();\n    }\n    return PerformanceMonitor.instance;\n  }\n  \n  private setupPerformanceObserver() {\n    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {\n      // Observe navigation timing\n      const navObserver = new PerformanceObserver((list) => {\n        list.getEntries().forEach((entry) => {\n          if (entry.entryType === 'navigation') {\n            const navEntry = entry as PerformanceNavigationTiming;\n            this.recordMetric('TTFB', navEntry.responseStart - navEntry.requestStart);\n            this.recordMetric('DOM_LOAD', navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart);\n            this.recordMetric('FULL_LOAD', navEntry.loadEventEnd - navEntry.loadEventStart);\n          }\n        });\n      });\n      \n      try {\n        navObserver.observe({ entryTypes: ['navigation'] });\n      } catch (e) {\n        console.warn('Navigation timing observer not supported:', e);\n      }\n      \n      // Observe resource timing\n      const resourceObserver = new PerformanceObserver((list) => {\n        list.getEntries().forEach((entry) => {\n          const resourceEntry = entry as PerformanceResourceTiming;\n          \n          // Track API calls\n          if (resourceEntry.name.includes('/api/')) {\n            this.recordMetric('API_RESPONSE', resourceEntry.duration);\n            this.checkBudget('API_RESPONSE', resourceEntry.duration);\n          }\n          \n          // Track static assets\n          if (resourceEntry.name.match(/\\.(js|css|png|jpg|jpeg|svg|webp|gif)$/)) {\n            this.recordMetric('STATIC_ASSET', resourceEntry.duration);\n          }\n        });\n      });\n      \n      try {\n        resourceObserver.observe({ entryTypes: ['resource'] });\n      } catch (e) {\n        console.warn('Resource timing observer not supported:', e);\n      }\n    }\n  }\n  \n  recordMetric(name: string, value: number) {\n    if (!this.metrics.has(name)) {\n      this.metrics.set(name, []);\n    }\n    \n    const values = this.metrics.get(name)!;\n    values.push(value);\n    \n    // Keep only last 100 measurements\n    if (values.length > 100) {\n      values.shift();\n    }\n    \n    // Track in analytics\n    trackPerformance(name, value, 'ms');\n  }\n  \n  checkBudget(metric: string, value: number) {\n    const budget = PERFORMANCE_BUDGETS[metric as keyof typeof PERFORMANCE_BUDGETS];\n    if (budget && value > budget) {\n      this.recordAlert(metric, value, budget);\n    }\n  }\n  \n  private recordAlert(metric: string, actual: number, budget: number) {\n    const alertKey = `${metric}_exceeded`;\n    const alertCount = this.alerts.get(alertKey) || 0;\n    \n    this.alerts.set(alertKey, alertCount + 1);\n    \n    // Log performance budget violation\n    console.warn(`Performance budget exceeded: ${metric}`, {\n      actual,\n      budget,\n      excess: actual - budget,\n      percentage: ((actual - budget) / budget) * 100,\n    });\n    \n    // Track in analytics\n    trackPerformance(`${metric}_BUDGET_EXCEEDED`, actual - budget, 'ms');\n    \n    // Send alert if in production and threshold exceeded\n    if (getEnv().NODE_ENV === 'production' && alertCount % 10 === 0) {\n      this.sendAlert(metric, actual, budget);\n    }\n  }\n  \n  private async sendAlert(metric: string, actual: number, budget: number) {\n    try {\n      // In a real implementation, this would send to monitoring service\n      // For now, we'll just log it\n      console.error('Performance Alert:', {\n        metric,\n        actual,\n        budget,\n        timestamp: new Date().toISOString(),\n        url: typeof window !== 'undefined' ? window.location.href : 'unknown',\n      });\n      \n      // Could send to external monitoring service\n      // await fetch('/api/monitoring/alert', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ metric, actual, budget }),\n      // });\n    } catch (error) {\n      console.error('Failed to send performance alert:', error);\n    }\n  }\n  \n  getMetrics() {\n    const result: Record<string, { avg: number; min: number; max: number; count: number }> = {};\n    \n    this.metrics.forEach((values, name) => {\n      if (values.length > 0) {\n        result[name] = {\n          avg: values.reduce((a, b) => a + b, 0) / values.length,\n          min: Math.min(...values),\n          max: Math.max(...values),\n          count: values.length,\n        };\n      }\n    });\n    \n    return result;\n  }\n  \n  getAlerts() {\n    return Object.fromEntries(this.alerts);\n  }\n  \n  clearMetrics() {\n    this.metrics.clear();\n    this.alerts.clear();\n  }\n}\n\n// Export singleton instance\nexport const performanceMonitor = PerformanceMonitor.getInstance();\n\n// Utility functions for manual performance tracking\nexport function startPerformanceTimer(name: string): () => void {\n  const startTime = performance.now();\n  \n  return () => {\n    const duration = performance.now() - startTime;\n    performanceMonitor.recordMetric(name, duration);\n    performanceMonitor.checkBudget(name, duration);\n    return duration;\n  };\n}\n\n// Decorator for measuring function performance\nexport function measurePerformance(name: string) {\n  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {\n    const originalMethod = descriptor.value;\n    \n    descriptor.value = async function (...args: any[]) {\n      const endTimer = startPerformanceTimer(`${name}_${propertyKey}`);\n      \n      try {\n        const result = await originalMethod.apply(this, args);\n        endTimer();\n        return result;\n      } catch (error) {\n        endTimer();\n        trackError(error as Error, `${name}_${propertyKey}`);\n        throw error;\n      }\n    };\n    \n    return descriptor;\n  };\n}\n\n// React hook for performance monitoring\nexport function usePerformanceMonitoring() {\n  const startTimer = (name: string) => startPerformanceTimer(name);\n  const getMetrics = () => performanceMonitor.getMetrics();\n  const getAlerts = () => performanceMonitor.getAlerts();\n  const clearMetrics = () => performanceMonitor.clearMetrics();\n  \n  return {\n    startTimer,\n    getMetrics,\n    getAlerts,\n    clearMetrics,\n  };\n}\n\n// Server-side performance monitoring\nexport function measureServerPerformance(name: string, fn: () => Promise<any>) {\n  return async function (...args: any[]) {\n    const startTime = Date.now();\n    \n    try {\n      const result = await fn.apply(this, args);\n      const duration = Date.now() - startTime;\n      \n      // Log server-side performance\n      console.log(`Server Performance: ${name} took ${duration}ms`);\n      \n      // Check against budgets\n      const budget = PERFORMANCE_BUDGETS[name as keyof typeof PERFORMANCE_BUDGETS];\n      if (budget && duration > budget) {\n        console.warn(`Server performance budget exceeded: ${name}`, {\n          duration,\n          budget,\n          excess: duration - budget,\n        });\n      }\n      \n      return result;\n    } catch (error) {\n      const duration = Date.now() - startTime;\n      console.error(`Server Performance Error: ${name} failed after ${duration}ms`, error);\n      throw error;\n    }\n  };\n}\n\n// Export performance budgets for reference\nexport { PERFORMANCE_BUDGETS };"